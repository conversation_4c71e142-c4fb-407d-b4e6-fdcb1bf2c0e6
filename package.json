{"name": "nextjs-supabase-gallery", "version": "0.4.6", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:local": "dotenv -e .env.docker -- next dev --turbopack", "build": "next build", "build:local": "dotenv -e .env.docker -- next build", "build:prod": "dotenv -e .env.production -- next build", "start": "next start", "start:local": "dotenv -e .env.docker -- next start", "start:prod": "dotenv -e .env.production -- next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "type-check": "tsc --noEmit", "supabase": "supabase", "db:push": "supabase db push", "db:reset": "supabase db reset"}, "dependencies": {"@auth/supabase-adapter": "^1.9.1", "@paypal/react-paypal-js": "^8.8.3", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.2", "stripe": "^18.2.0", "tailwind-merge": "^2.2.0", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.23.2", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.28.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "supabase": "^2.23.4", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}