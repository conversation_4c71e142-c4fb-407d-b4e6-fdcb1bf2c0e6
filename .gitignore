# Created by https://www.toptal.com/developers/gitignore/api/nextjs
# Edit at https://www.toptal.com/developers/gitignore?templates=nextjs

### NextJS ###
# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment variables
# Commit: .env (cloud default), .env.production, .env.example
# Never commit: .env.docker (local Docker Supabase overrides)
.env.docker
.env.docker.*
.env.local
.env.local.*
.env.production.*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# End of https://www.toptal.com/developers/gitignore/api/nextjs
Hyper Icon

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/
/.taskmaster/
/.cursor/
/.roo/