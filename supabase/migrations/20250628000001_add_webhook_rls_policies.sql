-- Add RLS policies to allow webhook operations for subscriptions and purchases
-- This enables service role operations for payment webhooks

-- Add service role policies for subscriptions table
DROP POLICY IF EXISTS "Service role can manage all subscriptions" ON public.subscriptions;
CREATE POLICY "Service role can manage all subscriptions"
  ON public.subscriptions FOR ALL
  USING (auth.role() = 'service_role');

-- Add service role policies for purchases table  
DROP POLICY IF EXISTS "Service role can manage all purchases" ON public.purchases;
CREATE POLICY "Service role can manage all purchases"
  ON public.purchases FOR ALL
  USING (auth.role() = 'service_role');

-- Add service role policies for profiles table (in case webhooks need to access user data)
DROP POLICY IF EXISTS "Service role can manage all profiles" ON public.profiles;
CREATE POLICY "Service role can manage all profiles"
  ON public.profiles FOR ALL
  USING (auth.role() = 'service_role');

-- Add comment to document the purpose
COMMENT ON POLICY "Service role can manage all subscriptions" ON public.subscriptions IS 'Allows webhook operations to create/update subscriptions without user authentication';
COMMENT ON POLICY "Service role can manage all purchases" ON public.purchases IS 'Allows webhook operations to create/update purchases without user authentication';
COMMENT ON POLICY "Service role can manage all profiles" ON public.profiles IS 'Allows webhook operations to access user profile data if needed';
