# =============================================================================
# ENVIRONMENT VARIABLES EXAMPLE
# =============================================================================
# Copy this file to .env.docker for local Docker Supabase development.
#
# Environment cases:
# 1. Local Next.js + Local Supabase → Copy to .env.docker
# 2. Local Next.js + Cloud Supabase → Use .env (default)
# 3. Vercel Next.js + Cloud Supabase → Use .env.production

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
PROJECT_ID=your_project_id_here

# =============================================================================
# DATABASE
# =============================================================================
DATABASE_URL=your_database_connection_string_here

# =============================================================================
# AUTHENTICATION
# =============================================================================
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
AUTH_SECRET=your_auth_secret_here

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================
# Google OAuth
SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID=your_google_client_id_here
SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=your_google_client_secret_here

# Facebook OAuth (optional)
SUPABASE_AUTH_EXTERNAL_FACEBOOK_CLIENT_ID=your_facebook_app_id_here
SUPABASE_AUTH_EXTERNAL_FACEBOOK_SECRET=your_facebook_app_secret_here

# =============================================================================
# FILE UPLOAD
# =============================================================================
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_URL=http://localhost:3000

# =============================================================================
# STRIPE PAYMENT PROCESSING
# =============================================================================
STRIPE_SECRET_KEY=your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Stripe Price IDs
STRIPE_STANDARD_MONTHLY_PRICE_ID=your_standard_monthly_price_id
STRIPE_STANDARD_YEARLY_PRICE_ID=your_standard_yearly_price_id
STRIPE_PREMIUM_MONTHLY_PRICE_ID=your_premium_monthly_price_id
STRIPE_PREMIUM_YEARLY_PRICE_ID=your_premium_yearly_price_id
STRIPE_COMMERCIAL_MONTHLY_PRICE_ID=your_commercial_monthly_price_id
STRIPE_COMMERCIAL_YEARLY_PRICE_ID=your_commercial_yearly_price_id

# =============================================================================
# PAYPAL PAYMENT PROCESSING
# =============================================================================
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_ENVIRONMENT=sandbox
PAYPAL_RETURN_URL=http://localhost:3000/account/subscriptions
PAYPAL_CANCEL_URL=http://localhost:3000/membership
PAYPAL_BASE_URL=https://api.sandbox.paypal.com
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id_here

# =============================================================================
# LOCAL DEVELOPMENT (Docker Supabase)
# =============================================================================
# These are typically only needed in .env.local for local Docker development
# SUPABASE_GRAPHQL_URL=http://127.0.0.1:54321/graphql/v1
# SUPABASE_STORAGE_URL=http://127.0.0.1:54321/storage/v1/s3
# SUPABASE_STUDIO_URL=http://127.0.0.1:54323
# SUPABASE_INBUCKET_URL=http://127.0.0.1:54324
# SUPABASE_S3_ACCESS_KEY=your_local_s3_access_key
# SUPABASE_S3_SECRET_KEY=your_local_s3_secret_key
# SUPABASE_S3_REGION=local
# SUPABASE_S3_STORAGE_URL=http://127.0.0.1:54321/storage/v1/s3

# =============================================================================
# DEVELOPMENT & DEBUG
# =============================================================================
# DEBUG_MODE=true
# LOG_LEVEL=info
# ENABLE_API_LOGS=true
