'use client'

import React from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorBoundaryFallbackProps>
}

interface ErrorBoundaryFallbackProps {
  error: Error
  resetError: () => void
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch() {
    // Error caught by boundary - could log to external service in production
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return (
        <FallbackComponent
          error={this.state.error || new Error('Unknown error')}
          resetError={this.resetError}
        />
      )
    }

    return this.props.children
  }
}

function DefaultErrorFallback({
  error,
  resetError,
}: ErrorBoundaryFallbackProps) {
  return (
    <div className='min-h-[400px] flex items-center justify-center p-4'>
      <div className='text-center space-y-4 max-w-md'>
        <div className='flex justify-center'>
          <div className='rounded-full bg-red-100 p-3'>
            <AlertTriangle className='h-8 w-8 text-red-600' />
          </div>
        </div>

        <div className='space-y-2'>
          <h2 className='text-xl font-semibold text-gray-900'>
            Something went wrong
          </h2>
          <p className='text-gray-600'>
            We encountered an unexpected error. Please try refreshing the page.
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details className='mt-4 p-3 bg-gray-100 rounded-md text-left'>
              <summary className='cursor-pointer font-medium text-sm'>
                Error Details (Development)
              </summary>
              <pre className='mt-2 text-xs text-red-600 whitespace-pre-wrap'>
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </details>
          )}
        </div>

        <div className='flex justify-center gap-2'>
          <Button onClick={resetError} variant='outline'>
            <RefreshCw className='h-4 w-4 mr-2' />
            Try Again
          </Button>
          <Button onClick={() => window.location.reload()}>Refresh Page</Button>
        </div>
      </div>
    </div>
  )
}

// Hook for using error boundary in functional components
export function useErrorHandler() {
  return (error: Error) => {
    throw error // Re-throw to be caught by error boundary
  }
}
