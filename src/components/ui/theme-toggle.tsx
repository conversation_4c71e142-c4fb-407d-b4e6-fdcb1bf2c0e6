'use client'

import { useState, useEffect } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { Sun, Moon, Monitor, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils/cn'

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className='w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 animate-pulse' />
    )
  }

  const themes = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Monitor },
  ] as const

  const currentTheme = themes.find(t => t.value === theme)
  const CurrentIcon = currentTheme?.icon || Sun

  return (
    <div className='relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center gap-2 px-3 py-2 rounded-lg transition-colors',
          'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700',
          'text-gray-700 dark:text-gray-300'
        )}
        aria-label='Toggle theme'
      >
        <CurrentIcon className='w-4 h-4' />
        <span className='text-sm font-medium hidden sm:block'>
          {currentTheme?.label}
        </span>
        <ChevronDown className={cn(
          'w-3 h-3 transition-transform hidden sm:block',
          isOpen && 'rotate-180'
        )} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className='fixed inset-0 z-10' 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className='absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20'>
            <div className='py-1'>
              {themes.map((themeOption) => {
                const Icon = themeOption.icon
                return (
                  <button
                    key={themeOption.value}
                    onClick={() => {
                      setTheme(themeOption.value)
                      setIsOpen(false)
                    }}
                    className={cn(
                      'w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors',
                      'hover:bg-gray-100 dark:hover:bg-gray-700',
                      theme === themeOption.value
                        ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                        : 'text-gray-700 dark:text-gray-300'
                    )}
                  >
                    <Icon className='w-4 h-4' />
                    {themeOption.label}
                    {theme === themeOption.value && (
                      <div className='ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full' />
                    )}
                  </button>
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
