import React from 'react'
import Link from 'next/link'

export const Footer = () => {
  return (
    <footer className='border-t bg-background'>
      <div className='container mx-auto px-4 py-8'>
        <div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
          {/* Brand */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-2'>
              <div className='w-6 h-6 bg-primary rounded flex items-center justify-center'>
                <svg
                  width='16'
                  height='16'
                  viewBox='0 0 24 24'
                  fill='none'
                  stroke='currentColor'
                  strokeWidth='2'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  className='text-primary-foreground'
                >
                  <rect x='3' y='3' width='18' height='18' rx='2' ry='2' />
                  <circle cx='9' cy='9' r='2' />
                  <path d='m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21' />
                </svg>
              </div>
              <span className='font-semibold'>Gallery</span>
            </div>
            <p className='text-sm text-muted-foreground'>
              A modern image gallery powered by Next.js and Supabase.
            </p>
          </div>

          {/* Features */}
          <div>
            <h3 className='font-semibold mb-4'>Features</h3>
            <ul className='space-y-2 text-sm text-muted-foreground'>
              <li>Image Upload</li>
              <li>Gallery View</li>
              <li>Secure Storage</li>
              <li>User Authentication</li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className='font-semibold mb-4'>Resources</h3>
            <ul className='space-y-2 text-sm'>
              <li>
                <Link
                  href='/docs'
                  className='text-muted-foreground hover:text-foreground transition-colors'
                >
                  Documentation
                </Link>
              </li>
              <li>
                <Link
                  href='/support'
                  className='text-muted-foreground hover:text-foreground transition-colors'
                >
                  Support
                </Link>
              </li>
              <li>
                <Link
                  href='/privacy'
                  className='text-muted-foreground hover:text-foreground hover:cursor-pointer transition-colors'
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href='/terms'
                  className='text-muted-foreground hover:text-foreground hover:cursor-pointer transition-colors'
                >
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Connect */}
          <div>
            <h3 className='font-semibold mb-4'>Connect</h3>
            <ul className='space-y-2 text-sm'>
              <li>
                <a
                  href='https://github.com'
                  className='text-muted-foreground hover:text-foreground transition-colors'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  GitHub
                </a>
              </li>
              <li>
                <a
                  href='https://twitter.com'
                  className='text-muted-foreground hover:text-foreground transition-colors'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  Twitter
                </a>
              </li>
              <li>
                <a
                  href='mailto:<EMAIL>'
                  className='text-muted-foreground hover:text-foreground transition-colors'
                >
                  Contact
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className='border-t mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center'>
          <p className='text-sm text-muted-foreground'>
            © 2024 Gallery. Built with Next.js and Supabase.
          </p>
          <div className='flex items-center space-x-4 mt-4 sm:mt-0'>
            <Link
              href='/privacy'
              className='text-xs text-muted-foreground hover:text-foreground hover:cursor-pointer transition-colors'
            >
              Privacy
            </Link>
            <Link
              href='/terms'
              className='text-xs text-muted-foreground hover:text-foreground hover:cursor-pointer transition-colors'
            >
              Terms
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
