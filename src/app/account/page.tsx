import { redirect } from 'next/navigation'
import {
  getSubscription,
  getUserDetails,
} from '@/lib/actions/subscription-simplified'
import { createClient } from '@/lib/supabase/server'
import CustomerPortalForm from './CustomerPortalForm'
import PayPalSubscriptionHandler from '@/components/paypal/paypal-subscription-handler'
import StripeSubscriptionHandler from '@/components/stripe/stripe-subscription-handler'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default async function AccountPage() {
  const supabase = await createClient()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/login')
  }

  const [subscription, userDetails] = await Promise.all([
    getSubscription(),
    getUserDetails(),
  ])

  return (
    <section className='mb-32 bg-black'>
      <PayPalSubscriptionHandler />
      <StripeSubscriptionHandler />
      <div className='max-w-6xl px-4 py-8 mx-auto sm:px-6 sm:pt-24 lg:px-8'>
        <div className='sm:align-center sm:flex sm:flex-col'>
          <h1 className='text-4xl font-extrabold text-white sm:text-center sm:text-6xl'>
            Account
          </h1>
          <p className='max-w-2xl m-auto mt-5 text-xl text-zinc-200 sm:text-center sm:text-2xl'>
            Manage your account and subscription details.
          </p>
        </div>
      </div>
      <div className='p-4'>
        <CustomerPortalForm subscription={subscription} />
        {!subscription && (
          <div className='mt-8 text-center'>
            <div className='bg-gray-900 rounded-lg p-8 max-w-md mx-auto'>
              <h3 className='text-xl font-semibold text-white mb-4'>
                No Active Subscription
              </h3>
              <p className='text-gray-300 mb-6'>
                Subscribe to get unlimited access to our premium gallery.
              </p>
              <Link href='/membership'>
                <Button className='w-full bg-pink-500 hover:bg-pink-600'>
                  View Subscription Plans
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
