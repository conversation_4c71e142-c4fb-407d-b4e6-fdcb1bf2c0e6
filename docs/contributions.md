## 🤖 Cline (DeepSeek)

- Authentication (login/signup)
- Image upload capabilities
- Gallery display with filtering
- Supabase backend integration for storage and database
- Various UI components and utilities

## 🎨 v0.dev (Vercel)

## 🧠 Colipot (<PERSON> 4)

## 🔥 cursor (<PERSON> 4)

## 🌊 Windsurf (codeium)

## Youtube (YouTube)

- [context7]() `use context7`
- [claude-task-master](https://github.com/eyaltoledano/claude-task-master?tab=readme-ov-file)
  `Initialize taskmaster-ai in my project`
- [`Tavily` API Key](https://app.tavily.com/home)

## User.ID Authentication Fix - Complete ✅

### Issue Resolved

Fixed NextJS API route authentication issue where server-side routes couldn't access user sessions established client-side, causing 401 Unauthorized errors during file uploads.

### Solution Implemented

**Fallback Authentication Strategy** in `/api/upload` route:

1. **Primary Method**: Try to authenticate via server-side cookies (preferred)
2. **Fallback Method**: If cookies fail, validate user_id from form data sent by authenticated client

### Database Schema ✅

- `user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE`
- `width INTEGER NOT NULL`
- `height INTEGER NOT NULL`
- All constraints properly enforced

### Upload Flow ✅

1. Client-side authentication works (user.id visible in header)
2. Upload form includes user_id in form data
3. Server validates user existence in database
4. Image dimensions extracted using Sharp library
5. File uploaded to Supabase Storage
6. Metadata inserted into images table with all required fields
